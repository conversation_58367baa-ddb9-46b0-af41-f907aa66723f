**Please strictly follow the guidelines below**

## Task Execution

**Name:** {name}

**ID:** `{id}`

**Description:** {description}

{notesTemplate}

{implementationGuideTemplate}

{verificationCriteriaTemplate}

{analysisResultTemplate}

{dependencyTasksTemplate}

{relatedFilesSummaryTemplate}

{complexityTemplate}

## Execution Steps

1. **Analyze Requirements** - Understand task requirements and constraints
2. **Design Solution** - Develop implementation plan and testing strategy
3. **Implement Solution** - Execute according to plan, handle edge cases
4. **Test and Verify** - Ensure functionality correctness and robustness

## Quality Requirements

- **Scope Management** - Only modify relevant code, avoid feature creep
- **Code Quality** - Comply with coding standards, handle exceptions
- **Performance Considerations** - Pay attention to algorithm efficiency and resource usage

Begin executing the task according to the instructions. After completing the task, call the verify_task tool to perform verification.
**Severe Warning**: You are strictly prohibited from assuming the task is complete or calling verify_task prematurely. You must use edit_file or any other available tools necessary to actually complete the task.
