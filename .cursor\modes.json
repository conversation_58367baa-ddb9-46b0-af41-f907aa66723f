{"modes": [{"name": "Task Planner", "description": "Plan tasks based on user requirements", "comment": "Task Planner - Creates and maintains task files", "model": "claude-3.7-sonnet", "customPrompt": "你是一個專業的任務規劃專家，你必須與用戶進行交互，分析用戶的需求，並收集專案相關資訊，最終使用 mcp_shrimp_task_manager_plan_task 建立任務，當任務建立完成後必須總結摘要，並告知用戶使用 任務執行 Model 進行任務執行。你必須專心於任務規劃禁止使用 mcp_shrimp_task_manager_execute_task 來執行任務，嚴重警告你是任務規劃專家，你不能直接修改程式碼，你只能規劃任務，並且你不能直接修改程式碼，你只能規劃任務。", "allowedCursorTools": ["codebase_search", "read_file", "edit_file", "list_directory", "grep_search", "file_search", "web"], "allowedMcpTools": ["mcp_shrimp_task_manager_plan_task", "mcp_shrimp_task_manager_analyze_task", "mcp_shrimp_task_manager_reflect_task", "mcp_shrimp_task_manager_split_tasks", "mcp_shrimp_task_manager_list_tasks", "mcp_shrimp_task_manager_delete_task", "mcp_shrimp_task_manager_clear_all_tasks", "mcp_shrimp_task_manager_update_task", "mcp_shrimp_task_manager_update_task_files", "mcp_shrimp_task_manager_list_conversation_log", "mcp_shrimp_task_manager_clear_conversation_log"], "autoApplyEdits": true, "autoRun": true, "autoFixErrors": true}, {"name": "Task Executor", "description": "Execute tasks", "comment": "Task Planner - Creates and maintains task files", "model": "claude-3.7-sonnet", "customPrompt": "你是一個專業的任務執行專家，當用戶有指定執行任務，則使用 mcp_shrimp_task_manager_execute_task 進行任務執行，沒有執行任務時則使用 mcp_shrimp_task_manager_list_tasks 尋找位執行的任務並執行，當執行完成後必須總結摘要告知用戶使用，你一次只能執行一個任務，單任務完成時除非用戶明確告知否則禁止進行下一則任務。用戶如果要求連續模式則按照順序連續執行所有任務", "allowedCursorTools": ["codebase_search", "read_file", "edit_file", "list_directory", "grep_search", "file_search", "web"], "allowedMcpTools": ["mcp_shrimp_task_manager_plan_task", "mcp_shrimp_task_manager_analyze_task", "mcp_shrimp_task_manager_reflect_task", "mcp_shrimp_task_manager_split_tasks", "mcp_shrimp_task_manager_list_tasks", "mcp_shrimp_task_manager_delete_task", "mcp_shrimp_task_manager_clear_all_tasks", "mcp_shrimp_task_manager_update_task", "mcp_shrimp_task_manager_update_task_files", "mcp_shrimp_task_manager_list_conversation_log", "mcp_shrimp_task_manager_clear_conversation_log", "mcp_shrimp_task_manager_execute_task", "mcp_shrimp_task_manager_verify_task", "mcp_shrimp_task_manager_complete_task"], "autoApplyEdits": true, "autoRun": true, "autoFixErrors": true}]}