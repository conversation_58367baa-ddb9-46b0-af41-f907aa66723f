<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title data-i18n-key="app_title">Shrimp Task Manager</title>
    <link rel="stylesheet" href="style.css" />
  </head>
  <body>
    <div class="container">
      <header>
        <h1 data-i18n-key="app_title">Shrimp Task Manager</h1>
        <div class="header-controls">
          <div class="status-bar">
            <div class="status-indicator"></div>
            <span data-i18n-key="status_indicator_online">ONLINE</span>
          </div>
          <select id="lang-switcher" aria-label="Select Language">
            <option value="en">English</option>
            <option value="zh-TW">繁體中文</option>
          </select>
        </div>
      </header>

      <div class="progress-indicator" id="progress-indicator">
        <div class="progress-bar-container">
          <div
            class="progress-segment progress-completed"
            id="progress-completed"
          ></div>
          <div
            class="progress-segment progress-in-progress"
            id="progress-in-progress"
          ></div>
          <div
            class="progress-segment progress-pending"
            id="progress-pending"
          ></div>
        </div>
        <div class="progress-labels" id="progress-labels">
          <!-- Labels will be generated by JS -->
        </div>
      </div>

      <main>
        <div class="dependency-view">
          <div class="panel-header">
            <h2 data-i18n-key="dependency_view_title">Dependency View</h2>
            <button id="reset-view-btn" class="reset-view-btn" title="Reset View" data-i18n-key="reset_view_btn_title">
              <svg viewBox="0 0 24 24" width="16" height="16">
                <path fill="currentColor" d="M12 5V2L8 6l4 4V7c3.31 0 6 2.69 6 6 0 2.97-2.17 5.43-5 5.91v2.02c3.95-.49 7-3.85 7-7.93 0-4.42-3.58-8-8-8zm-6 8c0-1.65.67-3.15 1.76-4.24L6.34 7.34C4.9 8.79 4 10.79 4 13c0 4.08 3.05 7.44 7 7.93v-2.02c-2.83-.48-5-2.94-5-5.91z"/>
              </svg>
            </button>
          </div>
          <div id="dependency-graph" class="dependency-graph">
            <p class="placeholder"></p>
          </div>
        </div>

        <div class="bottom-panels">
          <div class="task-panel">
            <div class="panel-header">
              <h2 data-i18n-key="task_list_title">Task List</h2>
              <div class="filters">
                <input
                  type="text"
                  id="search-input"
                  placeholder="Search tasks..."
                  data-i18n-key="search_placeholder"
                />
                <select id="sort-options">
                  <option
                    value="date-desc"
                    data-i18n-key="sort_option_date_desc"
                  >
                    Creation Time (New-Old)
                  </option>
                  <option value="date-asc" data-i18n-key="sort_option_date_asc">
                    Creation Time (Old-New)
                  </option>
                  <option value="name-asc" data-i18n-key="sort_option_name_asc">
                    Name (A-Z)
                  </option>
                  <option
                    value="name-desc"
                    data-i18n-key="sort_option_name_desc"
                  >
                    Name (Z-A)
                  </option>
                  <option value="status" data-i18n-key="sort_option_status">
                    Status
                  </option>
                </select>
                <select id="status-filter">
                  <option value="all" data-i18n-key="status_filter_all">
                    All Statuses
                  </option>
                  <option value="pending" data-i18n-key="status_filter_pending">
                    Pending
                  </option>
                  <option
                    value="in_progress"
                    data-i18n-key="status_filter_in_progress"
                  >
                    In Progress
                  </option>
                  <option
                    value="completed"
                    data-i18n-key="status_filter_completed"
                  >
                    Completed
                  </option>
                </select>
              </div>
            </div>

            <div class="task-list" id="task-list">
              <div class="loading"></div>
            </div>
          </div>

          <div class="task-details">
            <div class="panel-header">
              <h2 data-i18n-key="task_details_title">Task Details</h2>
            </div>
            <div id="task-details-content">
              <p class="placeholder"></p>
            </div>
          </div>
        </div>
      </main>

      <footer>
        <p>
          © 2023 Shrimp Task Manager - Current time:
          <span id="current-time"></span>
        </p>
      </footer>
    </div>

    <script src="script.js"></script>
    <script src="https://d3js.org/d3.v7.min.js"></script>
  </body>
</html>
